[gd_resource type="SpriteFrames" load_steps=18 format=3 uid="uid://qx7qkbtalpo0"]

[ext_resource type="Texture2D" uid="uid://dpfq3baex4gwi" path="res://assets/Sprites/Planet_LowDef_sprite_sheet_anim.png" id="1_qtt38"]

[sub_resource type="AtlasTexture" id="AtlasTexture_n6dhn"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 0, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_p0tbg"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 100, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_jkmg6"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 200, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_he2vk"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 300, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_s1y4q"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 400, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_8o4h3"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 500, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_fda1q"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 600, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_tbi03"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 700, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_08b1f"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 800, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_3260m"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 900, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_0p0cn"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 1000, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_075pf"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 1100, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_ndc4e"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 1200, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_lbojf"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 1300, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_fxc3x"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 1400, 100, 100)

[sub_resource type="AtlasTexture" id="AtlasTexture_670qi"]
atlas = ExtResource("1_qtt38")
region = Rect2(0, 1500, 100, 100)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_n6dhn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p0tbg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jkmg6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_he2vk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_s1y4q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8o4h3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fda1q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tbi03")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_08b1f")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3260m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0p0cn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_075pf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ndc4e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lbojf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fxc3x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_670qi")
}],
"loop": true,
"name": &"default",
"speed": 10.0
}]
