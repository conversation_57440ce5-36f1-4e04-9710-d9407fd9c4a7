extends Control

@onready var SAVE_SELECT = load("res://Scenes/Menus/SaveSelect/SaveSelect.tscn")
@onready var GENERAL_SETTINGS = load("res://Scenes/Menus/GeneralSettings.tscn")

# Called when the node enters the scene tree for the first time.
func _ready() -> void:
	pass # Replace with function body.


# Called every frame. 'delta' is the elapsed time since the previous frame.
func _process(_delta: float) -> void:
	pass


func _on_start_button_pressed() -> void:
	_play_button_click_sound()
	get_tree().change_scene_to_packed(SAVE_SELECT)


func _on_settings_button_pressed() -> void:
	_play_button_click_sound()
	get_tree().change_scene_to_packed(GENERAL_SETTINGS)


func _on_quit_button_pressed() -> void:
	_play_button_click_sound()
	get_tree().quit()


func _play_button_click_sound() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
