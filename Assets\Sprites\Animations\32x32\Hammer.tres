[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://w1x4rhu07dgh"]

[ext_resource type="Texture2D" uid="uid://c8ead5v6c41c" path="res://assets/Sprites/32x32/SpriteSheets/Hammer_sprite_sheet_anim.png" id="1_kwxve"]

[sub_resource type="AtlasTexture" id="AtlasTexture_phkdk"]
atlas = ExtResource("1_kwxve")
region = Rect2(0, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_3a1tx"]
atlas = ExtResource("1_kwxve")
region = Rect2(32, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_10cts"]
atlas = ExtResource("1_kwxve")
region = Rect2(64, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_iimrt"]
atlas = ExtResource("1_kwxve")
region = Rect2(96, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_sh4to"]
atlas = ExtResource("1_kwxve")
region = Rect2(128, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_08i5y"]
atlas = ExtResource("1_kwxve")
region = Rect2(160, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6qa10"]
atlas = ExtResource("1_kwxve")
region = Rect2(192, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_gd6fr"]
atlas = ExtResource("1_kwxve")
region = Rect2(224, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_7o4bw"]
atlas = ExtResource("1_kwxve")
region = Rect2(256, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_i8jou"]
atlas = ExtResource("1_kwxve")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ohpyg"]
atlas = ExtResource("1_kwxve")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_bloe5"]
atlas = ExtResource("1_kwxve")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_g61eg"]
atlas = ExtResource("1_kwxve")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_7uqn8"]
atlas = ExtResource("1_kwxve")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_k0hcc"]
atlas = ExtResource("1_kwxve")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ihb2q"]
atlas = ExtResource("1_kwxve")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_h5b6v"]
atlas = ExtResource("1_kwxve")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ijhat"]
atlas = ExtResource("1_kwxve")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_phg48"]
atlas = ExtResource("1_kwxve")
region = Rect2(0, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_bgfm5"]
atlas = ExtResource("1_kwxve")
region = Rect2(32, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_y6vn8"]
atlas = ExtResource("1_kwxve")
region = Rect2(64, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_evx51"]
atlas = ExtResource("1_kwxve")
region = Rect2(96, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_awdyb"]
atlas = ExtResource("1_kwxve")
region = Rect2(128, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_q72y0"]
atlas = ExtResource("1_kwxve")
region = Rect2(160, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_avg4q"]
atlas = ExtResource("1_kwxve")
region = Rect2(192, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_8u1ex"]
atlas = ExtResource("1_kwxve")
region = Rect2(224, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_1uhva"]
atlas = ExtResource("1_kwxve")
region = Rect2(256, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_4bytt"]
atlas = ExtResource("1_kwxve")
region = Rect2(0, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_hntpo"]
atlas = ExtResource("1_kwxve")
region = Rect2(32, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vnqm1"]
atlas = ExtResource("1_kwxve")
region = Rect2(64, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5rn2u"]
atlas = ExtResource("1_kwxve")
region = Rect2(96, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_sevy7"]
atlas = ExtResource("1_kwxve")
region = Rect2(128, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mf6ab"]
atlas = ExtResource("1_kwxve")
region = Rect2(160, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_boqbb"]
atlas = ExtResource("1_kwxve")
region = Rect2(192, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_yurm0"]
atlas = ExtResource("1_kwxve")
region = Rect2(224, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_rkv3k"]
atlas = ExtResource("1_kwxve")
region = Rect2(256, 32, 32, 32)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_phkdk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3a1tx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_10cts")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iimrt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sh4to")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_08i5y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6qa10")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gd6fr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7o4bw")
}],
"loop": false,
"name": &"East",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_i8jou")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ohpyg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bloe5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g61eg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7uqn8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_k0hcc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ihb2q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h5b6v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ijhat")
}],
"loop": false,
"name": &"North",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_phg48")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bgfm5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y6vn8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_evx51")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_awdyb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q72y0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_avg4q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8u1ex")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1uhva")
}],
"loop": false,
"name": &"South",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_4bytt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hntpo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vnqm1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5rn2u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_sevy7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mf6ab")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_boqbb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yurm0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rkv3k")
}],
"loop": false,
"name": &"West",
"speed": 9.0
}]
