class_name NodeConnection
extends Node2D

@export var primary_line: Line2D
@export var secondary_line: Line2D

var connection_duration  := 0.5
var expansion_duration   := 0.1
var contraction_duration := 0.2

var end_position: Vector2

signal connection_activated

func initialize(pos_a: Vector2, pos_b: Vector2):
	end_position = pos_b
	
	primary_line.add_point(pos_a)
	primary_line.add_point(pos_b)

	secondary_line.add_point(pos_a)
	secondary_line.add_point(pos_a)

func activate():
	var tween := get_tree().create_tween()

	# Animate line length
	var pos_a := secondary_line.get_point_position(0)
	tween.tween_method(
		func(p): secondary_line.set_point_position(1, p),
			pos_a, end_position, connection_duration
	).set_trans(Tween.TRANS_CUBIC)

	# Animate width expansion (after line length animation finishes)
	var normal_width   := secondary_line.width
	var expanded_width := normal_width * 4
	tween.tween_method(
		func(p): secondary_line.width = p,
			normal_width, expanded_width, expansion_duration
	).set_trans(Tween.TRANS_BOUNCE)

	# Animate width contraction (after expansion)
	tween.tween_method(
		func(p): secondary_line.width = p,
			expanded_width, normal_width, connection_duration
	).set_trans(Tween.TRANS_CUBIC)

	tween.finished.connect(connection_activated.emit)
