extends Node2D

@onready var RESEARCH_MAP_MENU = load("res://Scenes/Menus/ResearchMapMenu.tscn")
@onready var MAIN_MENU = load("res://Scenes/Menus/MainMenu.tscn")

func _on_back_button_pressed() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
	get_tree().change_scene_to_packed(RESEARCH_MAP_MENU)


func _on_exit_to_menu_button_pressed() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
	get_tree().change_scene_to_packed(MAIN_MENU)
