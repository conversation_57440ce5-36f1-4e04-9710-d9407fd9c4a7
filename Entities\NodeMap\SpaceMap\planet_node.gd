class_name PlanetNode
extends UnlockableNode


@export var planet_scene: PackedScene
@onready var selected_menu_animation_player: AnimationPlayer = $SelectedPlanetMenu/AnimationPlayer
@onready var selected_menu: Control = $SelectedPlanetMenu


func _ready() -> void:
	super._ready()
	PlanetSignalBus.planet_menu_opened.connect(_on_planet_menu_opened)


func _input_event(_viewport: Viewport, event: InputEvent, _shape_idx: int) -> void:
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT and event.pressed:
		match _node_state:
			NodeState.Enum.LOCKED:
				pass
			NodeState.Enum.UNLOCKED:
				_show_menu()
			NodeState.Enum.BEATEN:
				_show_menu()


func _on_planet_menu_opened(emitter: Node) -> void:
	if emitter == self:
		return
	selected_menu_animation_player.play_backwards("ShowMenu")
	await selected_menu_animation_player.animation_finished
	selected_menu.hide()


func _show_menu() -> void:
	PlanetSignalBus.planet_menu_opened.emit(self)
	selected_menu.show()
	selected_menu_animation_player.play("ShowMenu")


func _load_planet() -> void:
	if planet_scene != null:
		var planet_name := planet_scene.resource_path.get_file().get_basename()
		SaveManager.selected_planet_name = planet_name
		get_tree().change_scene_to_packed(planet_scene)

	# TODO some logic here to trigger
	if _node_state < NodeState.Enum.BEATEN:
		set_state(NodeState.Enum.BEATEN)


func _on_go_to_planet_button_pressed() -> void:
	_load_planet()
	selected_menu.hide()
