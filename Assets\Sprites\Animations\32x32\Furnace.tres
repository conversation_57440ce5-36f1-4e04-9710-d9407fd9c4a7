[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://d02wc4ossn0gs"]

[ext_resource type="Texture2D" uid="uid://tmaenl3xl0gu" path="res://assets/Sprites/32x32/SpriteSheets/Furnace_sprite_sheet_anim.png" id="1_rjsld"]

[sub_resource type="AtlasTexture" id="AtlasTexture_yj14h"]
atlas = ExtResource("1_rjsld")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_fibk2"]
atlas = ExtResource("1_rjsld")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_85mgy"]
atlas = ExtResource("1_rjsld")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_151d1"]
atlas = ExtResource("1_rjsld")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_u8kgg"]
atlas = ExtResource("1_rjsld")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_3qgot"]
atlas = ExtResource("1_rjsld")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_a6e7j"]
atlas = ExtResource("1_rjsld")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_4d4wa"]
atlas = ExtResource("1_rjsld")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_us31a"]
atlas = ExtResource("1_rjsld")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ipikb"]
atlas = ExtResource("1_rjsld")
region = Rect2(0, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6swms"]
atlas = ExtResource("1_rjsld")
region = Rect2(32, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_qk3q1"]
atlas = ExtResource("1_rjsld")
region = Rect2(64, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_g0yew"]
atlas = ExtResource("1_rjsld")
region = Rect2(96, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_jgxlp"]
atlas = ExtResource("1_rjsld")
region = Rect2(128, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_n6u1a"]
atlas = ExtResource("1_rjsld")
region = Rect2(160, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_v035b"]
atlas = ExtResource("1_rjsld")
region = Rect2(192, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ciagg"]
atlas = ExtResource("1_rjsld")
region = Rect2(224, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ol60x"]
atlas = ExtResource("1_rjsld")
region = Rect2(256, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_q053p"]
atlas = ExtResource("1_rjsld")
region = Rect2(0, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_4j2nb"]
atlas = ExtResource("1_rjsld")
region = Rect2(32, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_dcvdh"]
atlas = ExtResource("1_rjsld")
region = Rect2(64, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_yam3x"]
atlas = ExtResource("1_rjsld")
region = Rect2(96, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_uqf68"]
atlas = ExtResource("1_rjsld")
region = Rect2(128, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_q6yb7"]
atlas = ExtResource("1_rjsld")
region = Rect2(160, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_fifx1"]
atlas = ExtResource("1_rjsld")
region = Rect2(192, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_7or3t"]
atlas = ExtResource("1_rjsld")
region = Rect2(224, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_whiib"]
atlas = ExtResource("1_rjsld")
region = Rect2(256, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_l6gvp"]
atlas = ExtResource("1_rjsld")
region = Rect2(0, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_4n13l"]
atlas = ExtResource("1_rjsld")
region = Rect2(32, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_p3mvb"]
atlas = ExtResource("1_rjsld")
region = Rect2(64, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_tapw0"]
atlas = ExtResource("1_rjsld")
region = Rect2(96, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_xtwot"]
atlas = ExtResource("1_rjsld")
region = Rect2(128, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_3jvxe"]
atlas = ExtResource("1_rjsld")
region = Rect2(160, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ydnhn"]
atlas = ExtResource("1_rjsld")
region = Rect2(192, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_y474n"]
atlas = ExtResource("1_rjsld")
region = Rect2(224, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_03f7w"]
atlas = ExtResource("1_rjsld")
region = Rect2(256, 64, 32, 32)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_yj14h")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fibk2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_85mgy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_151d1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_u8kgg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3qgot")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a6e7j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4d4wa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_us31a")
}],
"loop": false,
"name": &"East",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ipikb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6swms")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qk3q1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g0yew")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jgxlp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n6u1a")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_v035b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ciagg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ol60x")
}],
"loop": false,
"name": &"North",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_q053p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4j2nb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dcvdh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yam3x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uqf68")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q6yb7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fifx1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7or3t")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_whiib")
}],
"loop": false,
"name": &"South",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_l6gvp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4n13l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p3mvb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tapw0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xtwot")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3jvxe")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ydnhn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y474n")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_03f7w")
}],
"loop": false,
"name": &"West",
"speed": 9.0
}]
