[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://xitsonu8ruad"]

[ext_resource type="Texture2D" uid="uid://ctiwmxcyn186a" path="res://assets/Sprites/32x32/SpriteSheets/Miner_sprite_sheet_anim.png" id="1_ssym2"]

[sub_resource type="AtlasTexture" id="AtlasTexture_bqini"]
atlas = ExtResource("1_ssym2")
region = Rect2(0, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vj0e8"]
atlas = ExtResource("1_ssym2")
region = Rect2(32, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_hcecb"]
atlas = ExtResource("1_ssym2")
region = Rect2(64, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ujtu2"]
atlas = ExtResource("1_ssym2")
region = Rect2(96, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_8888u"]
atlas = ExtResource("1_ssym2")
region = Rect2(128, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_3u6ei"]
atlas = ExtResource("1_ssym2")
region = Rect2(160, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_wxxas"]
atlas = ExtResource("1_ssym2")
region = Rect2(192, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mgmt7"]
atlas = ExtResource("1_ssym2")
region = Rect2(224, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_1imhk"]
atlas = ExtResource("1_ssym2")
region = Rect2(256, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_7hd0q"]
atlas = ExtResource("1_ssym2")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_omurw"]
atlas = ExtResource("1_ssym2")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_h3s3m"]
atlas = ExtResource("1_ssym2")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_7p3kt"]
atlas = ExtResource("1_ssym2")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_tccx4"]
atlas = ExtResource("1_ssym2")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mmjsm"]
atlas = ExtResource("1_ssym2")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_y8h05"]
atlas = ExtResource("1_ssym2")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_buixp"]
atlas = ExtResource("1_ssym2")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_kg7iq"]
atlas = ExtResource("1_ssym2")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_qa1nv"]
atlas = ExtResource("1_ssym2")
region = Rect2(0, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vugqn"]
atlas = ExtResource("1_ssym2")
region = Rect2(32, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_bwwgr"]
atlas = ExtResource("1_ssym2")
region = Rect2(64, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ffsca"]
atlas = ExtResource("1_ssym2")
region = Rect2(96, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mr3yr"]
atlas = ExtResource("1_ssym2")
region = Rect2(128, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ia366"]
atlas = ExtResource("1_ssym2")
region = Rect2(160, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_oorqj"]
atlas = ExtResource("1_ssym2")
region = Rect2(192, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ql64q"]
atlas = ExtResource("1_ssym2")
region = Rect2(224, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_pgi42"]
atlas = ExtResource("1_ssym2")
region = Rect2(256, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mpuu6"]
atlas = ExtResource("1_ssym2")
region = Rect2(0, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_2eec1"]
atlas = ExtResource("1_ssym2")
region = Rect2(32, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_kt2f2"]
atlas = ExtResource("1_ssym2")
region = Rect2(64, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_a8tab"]
atlas = ExtResource("1_ssym2")
region = Rect2(96, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_nr2r8"]
atlas = ExtResource("1_ssym2")
region = Rect2(128, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_4cjha"]
atlas = ExtResource("1_ssym2")
region = Rect2(160, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vb8o2"]
atlas = ExtResource("1_ssym2")
region = Rect2(192, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_gdapj"]
atlas = ExtResource("1_ssym2")
region = Rect2(224, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_cd0iq"]
atlas = ExtResource("1_ssym2")
region = Rect2(256, 32, 32, 32)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_bqini")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vj0e8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hcecb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ujtu2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8888u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3u6ei")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wxxas")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mgmt7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1imhk")
}],
"loop": true,
"name": &"East",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_7hd0q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_omurw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h3s3m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7p3kt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tccx4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mmjsm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y8h05")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_buixp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kg7iq")
}],
"loop": true,
"name": &"North",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_qa1nv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vugqn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bwwgr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ffsca")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mr3yr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ia366")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oorqj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ql64q")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pgi42")
}],
"loop": true,
"name": &"South",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_mpuu6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2eec1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kt2f2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a8tab")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nr2r8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4cjha")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vb8o2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gdapj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cd0iq")
}],
"loop": true,
"name": &"West",
"speed": 9.0
}]
