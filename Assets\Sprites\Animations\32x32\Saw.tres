[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://bry1m3sb4n7xr"]

[ext_resource type="Texture2D" uid="uid://tumn8wjiprhh" path="res://assets/Sprites/32x32/SpriteSheets/Saw_sprite_sheet_anim.png" id="1_cavcx"]

[sub_resource type="AtlasTexture" id="AtlasTexture_s45jd"]
atlas = ExtResource("1_cavcx")
region = Rect2(0, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_al54b"]
atlas = ExtResource("1_cavcx")
region = Rect2(32, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_mvb8w"]
atlas = ExtResource("1_cavcx")
region = Rect2(64, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ban4j"]
atlas = ExtResource("1_cavcx")
region = Rect2(96, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ryqkq"]
atlas = ExtResource("1_cavcx")
region = Rect2(128, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_t7q0v"]
atlas = ExtResource("1_cavcx")
region = Rect2(160, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_psdl0"]
atlas = ExtResource("1_cavcx")
region = Rect2(192, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6tdq6"]
atlas = ExtResource("1_cavcx")
region = Rect2(224, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_44ume"]
atlas = ExtResource("1_cavcx")
region = Rect2(256, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_m0351"]
atlas = ExtResource("1_cavcx")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_2fc11"]
atlas = ExtResource("1_cavcx")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_gwyjg"]
atlas = ExtResource("1_cavcx")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_hwnvn"]
atlas = ExtResource("1_cavcx")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_v2lbb"]
atlas = ExtResource("1_cavcx")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ei76i"]
atlas = ExtResource("1_cavcx")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_q1hjw"]
atlas = ExtResource("1_cavcx")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_8qif6"]
atlas = ExtResource("1_cavcx")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ob7n4"]
atlas = ExtResource("1_cavcx")
region = Rect2(256, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_buyg7"]
atlas = ExtResource("1_cavcx")
region = Rect2(0, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_c3g5s"]
atlas = ExtResource("1_cavcx")
region = Rect2(32, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_fdqiw"]
atlas = ExtResource("1_cavcx")
region = Rect2(64, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_1le2h"]
atlas = ExtResource("1_cavcx")
region = Rect2(96, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_m8uas"]
atlas = ExtResource("1_cavcx")
region = Rect2(128, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_yyubb"]
atlas = ExtResource("1_cavcx")
region = Rect2(160, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_bu326"]
atlas = ExtResource("1_cavcx")
region = Rect2(192, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_od4um"]
atlas = ExtResource("1_cavcx")
region = Rect2(224, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_pvr4t"]
atlas = ExtResource("1_cavcx")
region = Rect2(256, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_muk55"]
atlas = ExtResource("1_cavcx")
region = Rect2(0, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_7vnf3"]
atlas = ExtResource("1_cavcx")
region = Rect2(32, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_3kgi8"]
atlas = ExtResource("1_cavcx")
region = Rect2(64, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ovtnf"]
atlas = ExtResource("1_cavcx")
region = Rect2(96, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_kc2c4"]
atlas = ExtResource("1_cavcx")
region = Rect2(128, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_cop8v"]
atlas = ExtResource("1_cavcx")
region = Rect2(160, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_623b2"]
atlas = ExtResource("1_cavcx")
region = Rect2(192, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_dydmw"]
atlas = ExtResource("1_cavcx")
region = Rect2(224, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_lx3u0"]
atlas = ExtResource("1_cavcx")
region = Rect2(256, 32, 32, 32)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_s45jd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_al54b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mvb8w")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ban4j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ryqkq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_t7q0v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_psdl0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6tdq6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_44ume")
}],
"loop": false,
"name": &"East",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_m0351")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2fc11")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gwyjg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hwnvn")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_v2lbb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ei76i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q1hjw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8qif6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ob7n4")
}],
"loop": false,
"name": &"North",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_buyg7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c3g5s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fdqiw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1le2h")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m8uas")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yyubb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bu326")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_od4um")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pvr4t")
}],
"loop": false,
"name": &"South",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_muk55")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7vnf3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3kgi8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ovtnf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kc2c4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cop8v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_623b2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dydmw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_lx3u0")
}],
"loop": false,
"name": &"West",
"speed": 9.0
}]
