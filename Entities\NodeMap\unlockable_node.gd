class_name UnlockableNode
extends StaticBody2D

signal planet_state_changed(new_state: NodeState.Enum)

@onready var animation_player: AnimationPlayer = $AnimationPlayer

@export var NODE_INIT_STATE: NodeState.Enum = NodeState.Enum.LOCKED
@export var linked_nodes: Array[Node2D]


var _node_state := NodeState.Enum.LOCKED
var links         := []
@onready var connection_line: PackedScene = load("res://Entities/NodeMap/node_connection.tscn")


func get_state() -> NodeState.Enum:
	return _node_state


func set_state(new_state: NodeState.Enum):
	print("%s state changed to %s" % [self.name, NodeState.to_name(new_state)])
	if _node_state > new_state:
		return

	match new_state:
		NodeState.Enum.LOCKED:
			animation_player.play(&"locked")
		NodeState.Enum.UNLOCKED:
			animation_player.play(&"blink")
		NodeState.Enum.BEATEN:
			animation_player.play(&"RESET")
			for line in links:
				line.activate()

	_node_state = new_state
	planet_state_changed.emit(new_state)


func _create_links() -> void:
	for child: Node2D in linked_nodes:
		var node := child as UnlockableNode
		if node == null:
			push_error("Linked node is not a UnlockableNode.")
			continue

		var scene := connection_line.instantiate()
		var line  := scene as NodeConnection
		if line == null:
			push_error("Missing NodeConnection in scene.")
			continue

		line.initialize(self.global_position, child.global_position)
		line.connection_activated.connect(
			func():
				if node.get_state() == NodeState.Enum.LOCKED:
					node.set_state(NodeState.Enum.UNLOCKED)
		)
		links.append(line)

		get_tree().current_scene.add_child.call_deferred(scene)


func _ready() -> void:
	_create_links()
	set_state.call_deferred(NODE_INIT_STATE)
