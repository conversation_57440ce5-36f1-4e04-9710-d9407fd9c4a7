[gd_resource type="SpriteFrames" load_steps=38 format=3 uid="uid://dmuauo4oj028m"]

[ext_resource type="Texture2D" uid="uid://bnhcqxfsi32ec" path="res://assets/Sprites/32x32/SpriteSheets/Simpleconveyor_sprite_sheet_anim.png" id="1_f28rb"]

[sub_resource type="AtlasTexture" id="AtlasTexture_8ucnd"]
atlas = ExtResource("1_f28rb")
region = Rect2(0, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ltytx"]
atlas = ExtResource("1_f28rb")
region = Rect2(32, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5vac0"]
atlas = ExtResource("1_f28rb")
region = Rect2(64, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_n3gox"]
atlas = ExtResource("1_f28rb")
region = Rect2(96, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_pls66"]
atlas = ExtResource("1_f28rb")
region = Rect2(128, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_cy2fl"]
atlas = ExtResource("1_f28rb")
region = Rect2(160, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ab1lr"]
atlas = ExtResource("1_f28rb")
region = Rect2(192, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_37xod"]
atlas = ExtResource("1_f28rb")
region = Rect2(224, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_0efis"]
atlas = ExtResource("1_f28rb")
region = Rect2(256, 64, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_b4tkc"]
atlas = ExtResource("1_f28rb")
region = Rect2(0, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_g8gwb"]
atlas = ExtResource("1_f28rb")
region = Rect2(32, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_cqlbp"]
atlas = ExtResource("1_f28rb")
region = Rect2(64, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_j8k04"]
atlas = ExtResource("1_f28rb")
region = Rect2(96, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_7fx5h"]
atlas = ExtResource("1_f28rb")
region = Rect2(128, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6vb7l"]
atlas = ExtResource("1_f28rb")
region = Rect2(160, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_c6eex"]
atlas = ExtResource("1_f28rb")
region = Rect2(192, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_4lyb8"]
atlas = ExtResource("1_f28rb")
region = Rect2(224, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_j72em"]
atlas = ExtResource("1_f28rb")
region = Rect2(256, 96, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_1asyh"]
atlas = ExtResource("1_f28rb")
region = Rect2(0, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_vk5wv"]
atlas = ExtResource("1_f28rb")
region = Rect2(32, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_i3ihm"]
atlas = ExtResource("1_f28rb")
region = Rect2(64, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_aa128"]
atlas = ExtResource("1_f28rb")
region = Rect2(96, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_i2q0n"]
atlas = ExtResource("1_f28rb")
region = Rect2(128, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_i4dnf"]
atlas = ExtResource("1_f28rb")
region = Rect2(160, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_6lrxa"]
atlas = ExtResource("1_f28rb")
region = Rect2(192, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_n0pas"]
atlas = ExtResource("1_f28rb")
region = Rect2(224, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_w8cnn"]
atlas = ExtResource("1_f28rb")
region = Rect2(256, 32, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_7ltln"]
atlas = ExtResource("1_f28rb")
region = Rect2(0, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_8gwkm"]
atlas = ExtResource("1_f28rb")
region = Rect2(32, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_ua0i1"]
atlas = ExtResource("1_f28rb")
region = Rect2(64, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_nw1ah"]
atlas = ExtResource("1_f28rb")
region = Rect2(96, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_xoxti"]
atlas = ExtResource("1_f28rb")
region = Rect2(128, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_kfcyd"]
atlas = ExtResource("1_f28rb")
region = Rect2(160, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_xoxqm"]
atlas = ExtResource("1_f28rb")
region = Rect2(192, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_5sxpp"]
atlas = ExtResource("1_f28rb")
region = Rect2(224, 0, 32, 32)

[sub_resource type="AtlasTexture" id="AtlasTexture_wpxri"]
atlas = ExtResource("1_f28rb")
region = Rect2(256, 0, 32, 32)

[resource]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_8ucnd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ltytx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5vac0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n3gox")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pls66")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cy2fl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ab1lr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_37xod")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0efis")
}],
"loop": true,
"name": &"East",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_b4tkc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_g8gwb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cqlbp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j8k04")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7fx5h")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6vb7l")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_c6eex")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4lyb8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j72em")
}],
"loop": true,
"name": &"North",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_1asyh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vk5wv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i3ihm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_aa128")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i2q0n")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i4dnf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6lrxa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n0pas")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_w8cnn")
}],
"loop": true,
"name": &"South",
"speed": 9.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_7ltln")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8gwkm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ua0i1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nw1ah")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xoxti")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kfcyd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xoxqm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5sxpp")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wpxri")
}],
"loop": true,
"name": &"West",
"speed": 9.0
}]
