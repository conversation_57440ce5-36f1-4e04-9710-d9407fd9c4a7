class_name ResultsUI
extends Control

@onready var SPACE_MAP_MENU = load("res://Scenes/Menus/SpaceMapMenu.tscn")

func _ready() -> void:
	BuildingSignalBus.player_won.connect(display_results)
	hide()


func display_results() -> void:
	# TODO do more than just show the result screen
	show()


func _on_continue_button_pressed() -> void:
	_play_button_click_sound()
	hide()


func _on_back_button_pressed() -> void:
	_play_button_click_sound()
	save_map()
	# Player imperceptable wait in case saving takes more than instant
	await get_tree().create_timer(0.1).timeout
	_load_space_map()


func save_map() -> void:
	SaveManager.save_planet_map()

func _load_space_map() -> void:
	get_tree().change_scene_to_packed(SPACE_MAP_MENU)


func _play_button_click_sound() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
