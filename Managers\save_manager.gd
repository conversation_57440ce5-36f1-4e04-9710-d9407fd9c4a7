extends Node

const SAVE_ROOT = "res://saves"

## Called when old save slot is selected
@warning_ignore("unused_signal")
signal save_loaded()


## Called when new save slot is selected
@warning_ignore("unused_signal")
signal new_save_created()


var save_slot_name: String:
	set(value):
		save_slot_name = value
		
		if value.is_empty():
			selected_planet_name = ""


var selected_planet_name: String


var save_path: String:
	get():
		if save_slot_name.is_empty():
			return ""
		return "%s/%s" % [ SAVE_ROOT, save_slot_name ]


var has_save: bool:
	get():
		if save_slot_name.is_empty():
			return false
		return DirAccess.dir_exists_absolute(save_path)


var planet_save_path: String:
	get():
		if save_path.is_empty():
			return ""
		return "%s/%s" % [ save_path, "planet_states" ]


var has_planet_save_path: bool:
	get():
		if save_path.is_empty():
			return false
		return DirAccess.dir_exists_absolute(planet_save_path)


var planet_save_file: String:
	get():
		if planet_save_path.is_empty():
			return ""
		return "%s/%s.tscn" % [ planet_save_path, selected_planet_name ]


var has_planet_save_file: bool:
	get():
		if planet_save_path.is_empty():
			return false
		return FileAccess.file_exists(planet_save_file)


func try_load_planet_map() -> void:
	var Loaded_save: PackedScene
	
	if has_planet_save_file:
		Loaded_save = load(planet_save_file)
	else:
		print("Save file [%s] does not exist." % planet_save_file)
		return

	var buildings_node: Node2D = get_tree().get_current_scene().get_node("Buildings")
	var instance: Node2D = Loaded_save.instantiate()
	for bulding: Building in instance.get_children():
		bulding.bind_stats()
		bulding.reparent(buildings_node)
		bulding.owner = buildings_node
		if bulding is ItemHandlingBuilding:
			for item: Item in bulding.get_output_items():
				if item != null:
					item.reload_stats()
		
		# TODO put this in bulding.initialize()
		BuildingModeManager.occupied_tiles[bulding.tile_coordinates] = bulding
		BuildingModeManager._sync_animation(bulding, true)
	
	print("Loaded save: [%s]" % planet_save_file)


func save_planet_map() -> void:
	DirAccess.make_dir_recursive_absolute(planet_save_path)
	if FileAccess.file_exists(planet_save_file):
		var dir = DirAccess.open(planet_save_path)
		dir.remove_absolute(planet_save_file)
	
	var buildings = get_tree().get_current_scene().get_node("Buildings")
	var scene = PackedScene.new()
	scene.pack(buildings)
	var save_result = ResourceSaver.save(scene, planet_save_file)
	
	print("Saving planet: [%s]" % planet_save_file)
	if save_result != OK:
		push_error("Failed to save scene!")
