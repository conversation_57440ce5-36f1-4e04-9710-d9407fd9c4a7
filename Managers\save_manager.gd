extends Node

const SAVE_ROOT = "res://saves"

var global_name: String:
	set(value):
		if global_name != value:
			# save old data
			if not global_name.is_empty():
				save_global_data()

			global_name = value
			
			# load new data
			if not global_name.is_empty():
				load_global_data()

var save_path: String:
	get():
		if global_name.is_empty():
			return ""
		return "%s/%s" % [SAVE_ROOT, global_name]

var has_save: bool:
	get():
		if global_name.is_empty():
			return false
		return DirAccess.dir_exists_absolute(save_path)

var global_file: String:
	get():
		if save_path.is_empty():
			return ""
		return "%s/global_data.json" % save_path

var has_global_file: bool:
	get():
		if save_path.is_empty():
			return false
		return FileAccess.file_exists(global_file)

var global_data: Dictionary = {}
var planet_data: Dictionary = {}

## Load global data (research, unlocks, etc.)
func load_global_data() -> void:
	if not has_global_file:
		print("global file [%s] does not exist." % global_file)
		return

	var file = FileAccess.open(global_file, FileAccess.READ)
	if file == null:
		push_error("Failed to open global file: %s" % global_file)
		return

	var json_string = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_string)
	if parse_result != OK:
		push_error("Failed to parse global JSON: %s" % json.get_error_message())
		return

	global_data = json.data
	print("Loading global data: [%s]" % global_file)


## Save global data (research, unlocks, etc.)
func save_global_data() -> void:
	DirAccess.make_dir_recursive_absolute(save_path)

	print("Saving global data: [%s]" % global_file)

	var json_string = JSON.stringify(global_data, "\t")
	var file = FileAccess.open(global_file, FileAccess.WRITE)
	if file == null:
		push_error("Failed to create global file: %s" % global_file)
		return

	file.store_string(json_string)
	file.close()

## Generate a unique save name for a component
func get_component_save_name(node: Node) -> String:
	var component_name: String
	if node.get_script():
		component_name = node.get_script().get_global_name()
		if component_name.is_empty():
			component_name = node.get_script().resource_path.get_file().get_basename()
	else:
		component_name = node.get_class()

	var node_path = str(node.get_path())
	return "%s_%s" % [component_name, node_path.hash()]
