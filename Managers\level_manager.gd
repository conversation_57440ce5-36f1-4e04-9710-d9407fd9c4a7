extends Node2D

class_name LevelManager

var inventory : ResourceInventory

func _ready() -> void:
	inventory=ResourceInventory.new()
	for item in ItemType.get_storable_items():
		if item not in inventory.resources:
			inventory.resources[item]=0
	
	SaveManager.try_load_planet_map()

#
#
#func _unhandled_input(event: InputEvent) -> void:
	#if event.pressed and event.keycode == KEY_P:
		#print(inventory.resources)
	#if event.pressed and event.keycode == KEY_O:
		#var tmp = {ItemType.Enum.IRON_INGOT:100}
		#inventory.consume_resources(tmp)
	#if event.pressed and event.keycode == KEY_I:
		#inventory.add_resource(100, ItemType.Enum.IRON_INGOT)
		#inventory.add_resource(100, ItemType.Enum.COPPER_INGOT)
