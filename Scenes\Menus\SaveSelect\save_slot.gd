extends Panel

@onready var SPACE_MAP_MENU = load("res://Scenes/Menus/SpaceMapMenu.tscn")

@onready var new_game_option: VBoxContainer = $NewGameOption
@onready var continue_game_option: VBoxContainer = $ContinueGameOption


func _ready() -> void:
	SaveManager.save_slot_name = name
	if SaveManager.has_save:
		continue_game_option.visible = true
		new_game_option.visible = false
	else:
		continue_game_option.visible = false
		new_game_option.visible = true


func _on_continue_button_pressed() -> void:
	SaveManager.save_slot_name = name
	SaveManager.save_loaded.emit()
	_play_button_click_sound()
	get_tree().change_scene_to_packed(SPACE_MAP_MENU)
	
	
func _on_new_game_button_pressed() -> void:
	SaveManager.save_slot_name = name
	SaveManager.new_save_created.emit()
	_play_button_click_sound()
	get_tree().change_scene_to_packed(SPACE_MAP_MENU)
	
	
func _play_button_click_sound() -> void:
	AudioManager.create_audio(SoundEffect.SOUND_EFFECT_TYPE.UI_BUTTON_CLICKED)
