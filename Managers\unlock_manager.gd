extends Node

## Array of Unlocked buildings
@export var buildings_unlocked: Array[BuildingStats]

## Dictionary of Unlocked simple building recipies (only one resource as input)
@export var unlocked_simple_recipes_catalog: Dictionary[BuildingType.Enum, Dictionary]

## Dictionary of Unlocked complex building recipies (multiple resources as input)
@export var unlocked_complex_recipes_catalog: Dictionary[BuildingType.Enum, Array]


func _ready() -> void:
	_unlock_defaults()
	print_unlocked_recipes()


func reset() -> void:
	buildings_unlocked.clear()
	unlocked_simple_recipes_catalog.clear()
	unlocked_complex_recipes_catalog.clear()
	
	_unlock_defaults()


func _unhandled_input(event: InputEvent) -> void:
	if event.is_action_pressed(&"recipe_unlock"):
		var buildings_to_unlock := BuildingManager.buildings
		unlock_buildings(buildings_to_unlock)
		
		var recipes_to_unlock: Array[ItemRecipe] = []
		for building_recipes in ItemRecipesHandler.simple_recipes_catalog.values():
			for recipe in building_recipes.values():
				recipes_to_unlock.push_back(recipe)

		for building_recipes in ItemRecipesHandler.complex_recipes_catalog.values():
			for recipe in building_recipes:
				recipes_to_unlock.push_back(recipe)
		
		unlock_item_recipes(recipes_to_unlock)
		
		print_unlocked_recipes()

## Print unlocked recipies
func print_unlocked_recipes() -> void:
	print("Unlocked buildings:")
	BuildingManager.print(buildings_unlocked)
	
	print("Unlocked simple recipes:")
	for building_recipes in unlocked_simple_recipes_catalog.values():
		ItemRecipesHandler.print(building_recipes.values())

	print("Unlocked complex recipes:")
	for building_recipes in unlocked_complex_recipes_catalog.values():
		ItemRecipesHandler.print(building_recipes)


func _unlock_defaults():
	var buildings = BuildingManager.buildings
	for building in buildings:
		if not building.unlocked_by_default:
			continue
	
		buildings_unlocked.push_back(building)
	
	var simple_global_recipes := ItemRecipesHandler.simple_recipes_catalog
	for building in simple_global_recipes.keys():
		var building_recipes := simple_global_recipes[building]
		for input_resource in building_recipes.keys():
			var recipe: ItemRecipe = building_recipes[input_resource]
			if not recipe.unlocked_by_default:
				continue
			
			var unlocked_building_recipes: Dictionary = unlocked_simple_recipes_catalog.get_or_add(building, {})
			unlocked_building_recipes[input_resource] = recipe
	
	var complex_global_recipes := ItemRecipesHandler.complex_recipes_catalog
	for building in complex_global_recipes.keys():
		var building_recipes := complex_global_recipes[building]
		for recipe: ItemRecipe in building_recipes:
			if not recipe.unlocked_by_default:
				continue
			
			var unlocked_building_recipes: Array = unlocked_complex_recipes_catalog.get_or_add(building, [])
			unlocked_building_recipes.push_back(recipe)


func unlock_item_recipes(recipes_to_unlock: Array[ItemRecipe]):
	for recipe in recipes_to_unlock:
		var building := recipe.processing_building
		if recipe.input_resources.size() <= 1:
			var input_resource: ItemData = recipe.input_resources.keys().front()
			var unlocked_building_recipes: Dictionary = unlocked_simple_recipes_catalog.get_or_add(building, {})
			unlocked_building_recipes[input_resource] = recipe
		else:
			var unlocked_building_recipes: Array = unlocked_complex_recipes_catalog.get_or_add(building, [])
			if unlocked_building_recipes.has(recipe):
				continue
			unlocked_building_recipes.push_back(recipe)


func unlock_buildings(buildings_to_unlock: Array[BuildingStats]):
	for building in buildings_to_unlock:
		if buildings_unlocked.has(building):
			continue
		buildings_unlocked.push_back(building)


## Return Unlocked simple building recipies (only one resource as input) 
func get_simple_recipe(building: BuildingType.Enum, input: ItemData) -> ItemRecipe:
	var building_recipes: Dictionary = unlocked_simple_recipes_catalog.get(building,{})
	var recipe: ItemRecipe = building_recipes.get(input)
	return recipe


## Return Unlocked complex building recipies (multiple resources as input)
func get_complex_recipes(building: BuildingType.Enum) -> Array[ItemRecipe]:
	var building_recipes: Array[ItemRecipe] = unlocked_complex_recipes_catalog.get(building,{})
	return building_recipes


## Checks if given recipe can be crafted
func has_enought_resources_for_recipes(recipe: ItemRecipe, items_available: Dictionary[ItemData,int]) -> bool:
	return ItemRecipesHandler.has_enought_resources_for_recipes(recipe,items_available)
